//! 认证系统
//! 对应原文件: src/server/auth.mjs

use axum::{
    extract::{Request, State},
    http::{header::AUTHORIZATION, StatusCode},
    middleware::Next,
    response::Response,
    Json,
};
use jsonwebtoken::{decode, encode, Decod<PERSON><PERSON><PERSON>, <PERSON>co<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use chrono::{Duration, Utc};
use anyhow::Result;
use fount_types::{User, LoginRequest, LoginResponse};

/// JWT Claims
#[derive(Debug, Serialize, Deserialize)]
pub struct Claims {
    pub sub: String,  // 用户ID
    pub exp: i64,     // 过期时间
    pub iat: i64,     // 签发时间
    pub role: String, // 用户角色
}

/// 认证服务
pub struct AuthService {
    encoding_key: EncodingKey,
    decoding_key: Decoding<PERSON><PERSON>,
}

impl AuthService {
    /// 创建新的认证服务
    pub fn new(secret: &str) -> Self {
        Self {
            encoding_key: EncodingKey::from_secret(secret.as_ref()),
            decoding_key: Decoding<PERSON>ey::from_secret(secret.as_ref()),
        }
    }
    
    /// 生成JWT令牌
    pub fn generate_token(&self, user_id: &str, role: &str) -> Result<String> {
        let now = Utc::now();
        let exp = now + Duration::hours(24);
        
        let claims = Claims {
            sub: user_id.to_string(),
            exp: exp.timestamp(),
            iat: now.timestamp(),
            role: role.to_string(),
        };
        
        let token = encode(&Header::default(), &claims, &self.encoding_key)?;
        Ok(token)
    }
    
    /// 验证JWT令牌
    pub fn verify_token(&self, token: &str) -> Result<Claims> {
        let token_data = decode::<Claims>(
            token,
            &self.decoding_key,
            &Validation::default(),
        )?;
        Ok(token_data.claims)
    }
    
    /// 用户登录
    pub async fn login(&self, request: LoginRequest) -> Result<LoginResponse> {
        // TODO: 实现用户验证逻辑
        // 这里应该查询数据库验证用户名和密码
        
        // 临时实现
        if request.username == "admin" && request.password == "password" {
            let token = self.generate_token(&request.username, "admin")?;
            let user = User {
                id: request.username.clone(),
                username: request.username,
                email: "<EMAIL>".to_string(),
                display_name: "Administrator".to_string(),
                avatar_url: None,
                permission_level: fount_types::PermissionLevel::Admin,
                status: fount_types::Status::Active,
                created_at: Utc::now(),
                updated_at: Utc::now(),
                last_login: Some(Utc::now()),
            };
            
            Ok(LoginResponse {
                user,
                token,
                expires_at: Utc::now() + Duration::hours(24),
            })
        } else {
            Err(anyhow::anyhow!("Invalid credentials"))
        }
    }
}

/// 认证中间件
pub async fn auth_middleware(
    State(auth_service): State<Arc<AuthService>>,
    mut request: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    let auth_header = request
        .headers()
        .get(AUTHORIZATION)
        .and_then(|header| header.to_str().ok());
    
    if let Some(auth_header) = auth_header {
        if let Some(token) = auth_header.strip_prefix("Bearer ") {
            match auth_service.verify_token(token) {
                Ok(claims) => {
                    // 将用户信息添加到请求扩展中
                    request.extensions_mut().insert(claims);
                    Ok(next.run(request).await)
                }
                Err(_) => Err(StatusCode::UNAUTHORIZED),
            }
        } else {
            Err(StatusCode::UNAUTHORIZED)
        }
    } else {
        Err(StatusCode::UNAUTHORIZED)
    }
}
