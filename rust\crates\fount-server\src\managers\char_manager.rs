//! 角色管理器
//! 对应原文件: src/server/managers/char_manager.mjs

use std::collections::HashMap;
use uuid::Uuid;
use anyhow::Result;
use fount_types::{Character, CreateCharacterRequest, UpdateCharacterRequest, SearchCharacterRequest};

/// 角色管理器
pub struct CharacterManager {
    characters: HashMap<Uuid, Character>,
}

impl CharacterManager {
    /// 创建新的角色管理器
    pub fn new() -> Self {
        Self {
            characters: HashMap::new(),
        }
    }
    
    /// 创建角色
    pub async fn create_character(&mut self, request: CreateCharacterRequest, creator_id: String) -> Result<Character> {
        let character = Character {
            id: Uuid::new_v4(),
            name: request.name,
            description: request.description,
            avatar_url: request.avatar_url,
            personality: request.personality,
            background: request.background,
            greeting: request.greeting,
            example_conversations: request.example_conversations,
            tags: request.tags,
            creator_id,
            status: fount_types::Status::Active,
            created_at: chrono::Utc::now(),
            updated_at: chrono::Utc::now(),
            metadata: request.metadata.unwrap_or_default(),
        };
        
        self.characters.insert(character.id, character.clone());
        Ok(character)
    }
    
    /// 获取角色
    pub async fn get_character(&self, id: &Uuid) -> Option<&Character> {
        self.characters.get(id)
    }
    
    /// 更新角色
    pub async fn update_character(&mut self, id: &Uuid, request: UpdateCharacterRequest) -> Result<Option<Character>> {
        if let Some(character) = self.characters.get_mut(id) {
            if let Some(name) = request.name {
                character.name = name;
            }
            if let Some(description) = request.description {
                character.description = description;
            }
            if let Some(avatar_url) = request.avatar_url {
                character.avatar_url = avatar_url;
            }
            if let Some(personality) = request.personality {
                character.personality = personality;
            }
            if let Some(background) = request.background {
                character.background = background;
            }
            if let Some(greeting) = request.greeting {
                character.greeting = greeting;
            }
            if let Some(example_conversations) = request.example_conversations {
                character.example_conversations = example_conversations;
            }
            if let Some(tags) = request.tags {
                character.tags = tags;
            }
            if let Some(status) = request.status {
                character.status = status;
            }
            if let Some(metadata) = request.metadata {
                character.metadata = metadata;
            }
            
            character.updated_at = chrono::Utc::now();
            Ok(Some(character.clone()))
        } else {
            Ok(None)
        }
    }
    
    /// 删除角色
    pub async fn delete_character(&mut self, id: &Uuid) -> Result<bool> {
        Ok(self.characters.remove(id).is_some())
    }
    
    /// 搜索角色
    pub async fn search_characters(&self, request: SearchCharacterRequest) -> Result<Vec<Character>> {
        let mut results: Vec<Character> = self.characters.values().cloned().collect();
        
        // 按查询条件过滤
        if let Some(query) = &request.query {
            let query_lower = query.to_lowercase();
            results.retain(|c| {
                c.name.to_lowercase().contains(&query_lower) ||
                c.description.to_lowercase().contains(&query_lower)
            });
        }
        
        if let Some(tags) = &request.tags {
            results.retain(|c| {
                tags.iter().any(|tag| c.tags.contains(tag))
            });
        }
        
        if let Some(creator_id) = &request.creator_id {
            results.retain(|c| &c.creator_id == creator_id);
        }
        
        if let Some(status) = &request.status {
            results.retain(|c| &c.status == status);
        }
        
        // 分页
        let page = request.page.unwrap_or(1);
        let per_page = request.per_page.unwrap_or(20);
        let start = ((page - 1) * per_page) as usize;
        let end = (start + per_page as usize).min(results.len());
        
        if start < results.len() {
            Ok(results[start..end].to_vec())
        } else {
            Ok(vec![])
        }
    }
    
    /// 获取角色总数
    pub fn get_character_count(&self) -> usize {
        self.characters.len()
    }
    
    /// 按创建者获取角色
    pub async fn get_characters_by_creator(&self, creator_id: &str) -> Vec<&Character> {
        self.characters.values()
            .filter(|c| c.creator_id == creator_id)
            .collect()
    }
}

impl Default for CharacterManager {
    fn default() -> Self {
        Self::new()
    }
}
