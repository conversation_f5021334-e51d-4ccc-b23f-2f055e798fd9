//! 主Web服务器
//! 对应原文件: src/server/server.mjs

use axum::{
    Router,
    routing::{get, post},
    extract::State,
    response::Html,
    http::StatusCode,
};
use tower::ServiceBuilder;
use tower_http::{
    cors::CorsLayer,
    trace::TraceLayer,
    services::ServeDir,
    compression::CompressionLayer,
};
use std::sync::Arc;
use anyhow::Result;
use crate::{ServerConfig, endpoints::*};

/// Fount服务器主结构
pub struct FountServer {
    config: ServerConfig,
    app: Router,
}

impl FountServer {
    /// 创建新的服务器实例
    pub async fn new(config: ServerConfig) -> Result<Self> {
        let app = create_app(&config).await?;
        
        Ok(Self {
            config,
            app,
        })
    }
    
    /// 启动服务器
    pub async fn start(self) -> Result<()> {
        let addr = format!("{}:{}", self.config.host, self.config.port);
        let listener = tokio::net::TcpListener::bind(&addr).await?;
        
        tracing::info!("Server listening on {}", addr);
        
        axum::serve(listener, self.app).await?;
        Ok(())
    }
}

/// 创建应用路由
async fn create_app(config: &ServerConfig) -> Result<Router> {
    let app = Router::new()
        // 静态文件服务
        .nest_service("/static", ServeDir::new(&config.static_dir))
        
        // API路由
        .route("/api/health", get(health_check))
        .route("/api/auth/login", post(login))
        .route("/api/auth/logout", post(logout))
        .route("/api/characters", get(list_characters).post(create_character))
        .route("/api/characters/:id", get(get_character).put(update_character).delete(delete_character))
        .route("/api/chat", post(chat))
        
        // 主页面
        .route("/", get(index_page))
        .fallback(not_found)
        
        // 中间件
        .layer(
            ServiceBuilder::new()
                .layer(TraceLayer::new_for_http())
                .layer(CorsLayer::permissive())
                .layer(CompressionLayer::new())
        )
        .with_state(Arc::new(config.clone()));
    
    Ok(app)
}

/// 主页面处理器
async fn index_page() -> Html<&'static str> {
    Html(include_str!("../../../static/index.html"))
}

/// 404处理器
async fn not_found() -> StatusCode {
    StatusCode::NOT_FOUND
}

/// 健康检查
async fn health_check() -> &'static str {
    "OK"
}
