//! AI源类型定义
//! 对应原文件: src/decl/AIsource.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use async_trait::async_trait;
use crate::{ApiResponse, Status, Info, Locale, PromptStruct};

/// 分词器接口 (对应 tokenizer_t)
#[async_trait]
pub trait Tokenizer<InputType, TokenType>
where
    InputType: Send + Sync,
    TokenType: Send + Sync,
{
    /// 释放资源
    async fn free(&mut self) -> crate::Result<()>;

    /// 编码输入为token
    fn encode(&self, prompt: InputType) -> Vec<TokenType>;

    /// 解码token为输入
    fn decode(&self, tokens: Vec<TokenType>) -> InputType;

    /// 解码单个token
    fn decode_single(&self, token: TokenType) -> InputType;

    /// 获取token数量
    fn get_token_count(&self, prompt: InputType) -> usize;
}

/// AI源信息接口 (对应 interfaces.info)
#[async_trait]
pub trait AiSourceInfoInterface {
    async fn update_info(&self, locales: Vec<Locale>) -> crate::Result<Info>;
}

/// AI源接口 (对应 AIsource_t)
#[async_trait]
pub trait AiSource<InputType, OutputType>
where
    InputType: Send + Sync,
    OutputType: Send + Sync,
{
    /// 获取文件名
    fn get_filename(&self) -> &str;

    /// 获取类型
    fn get_type(&self) -> &str;

    /// 获取信息
    fn get_info(&self) -> &Info;

    /// 是否付费
    fn is_paid(&self) -> bool;

    /// 获取扩展信息
    fn get_extension(&self) -> &HashMap<String, serde_json::Value>;

    /// 卸载AI源
    async fn unload(&mut self) -> crate::Result<()>;

    /// 调用AI源
    async fn call(&self, prompt: InputType) -> crate::Result<OutputType>;

    /// 获取信息接口
    fn info_interface(&self) -> Option<&dyn AiSourceInfoInterface>;

    /// 获取分词器
    fn tokenizer(&self) -> Option<&dyn Tokenizer<InputType, serde_json::Value>>;
}

/// 文本AI源接口 (对应 textAISource_t)
#[async_trait]
pub trait TextAiSource: AiSource<String, String> {
    /// 结构化调用
    async fn struct_call(&self, prompt_struct: PromptStruct) -> crate::Result<String>;
}

/// AI源配置
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiSourceConfig {
    pub id: Uuid,
    pub name: String,
    pub provider: String,
    pub model: String,
    pub api_key: Option<String>,
    pub endpoint: String,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// AI源创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAiSourceRequest {
    pub name: String,
    pub provider: String,
    pub model: String,
    pub api_key: Option<String>,
    pub endpoint: String,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// AI源更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAiSourceRequest {
    pub name: Option<String>,
    pub provider: Option<String>,
    pub model: Option<String>,
    pub api_key: Option<String>,
    pub endpoint: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub status: Option<Status>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// AI消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiMessage {
    pub role: String,
    pub content: String,
    pub name: Option<String>,
    pub function_call: Option<serde_json::Value>,
    pub tool_calls: Option<Vec<serde_json::Value>>,
}

/// AI对话请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiChatRequest {
    pub source_id: Uuid,
    pub messages: Vec<AiMessage>,
    pub character_id: Option<Uuid>,
    pub user_id: String,
    pub stream: Option<bool>,
}

/// AI对话响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiChatResponse {
    pub message: AiMessage,
    pub usage: Option<AiUsage>,
    pub finish_reason: Option<String>,
}

/// AI使用统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

/// AI源响应类型别名
pub type AiSourceResponse = ApiResponse<AiSourceConfig>;
pub type AiSourceListResponse = ApiResponse<Vec<AiSourceConfig>>;
pub type AiChatApiResponse = ApiResponse<AiChatResponse>;

/// AI源创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateAiSourceRequest {
    pub name: String,
    pub provider: String,
    pub model: String,
    pub api_key: Option<String>,
    pub endpoint: String,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// AI源更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateAiSourceRequest {
    pub name: Option<String>,
    pub provider: Option<String>,
    pub model: Option<String>,
    pub api_key: Option<String>,
    pub endpoint: Option<String>,
    pub max_tokens: Option<u32>,
    pub temperature: Option<f32>,
    pub top_p: Option<f32>,
    pub frequency_penalty: Option<f32>,
    pub presence_penalty: Option<f32>,
    pub status: Option<Status>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// AI消息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiMessage {
    pub role: String,
    pub content: String,
    pub timestamp: DateTime<Utc>,
}

/// AI对话请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiChatRequest {
    pub source_id: Uuid,
    pub messages: Vec<AiMessage>,
    pub character_id: Option<Uuid>,
    pub user_id: String,
    pub stream: Option<bool>,
}

/// AI对话响应
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiChatResponse {
    pub message: AiMessage,
    pub usage: Option<AiUsage>,
    pub finish_reason: Option<String>,
}

/// AI使用统计
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AiUsage {
    pub prompt_tokens: u32,
    pub completion_tokens: u32,
    pub total_tokens: u32,
}

/// AI源响应类型别名
pub type AiSourceResponse = ApiResponse<AiSource>;
pub type AiSourceListResponse = ApiResponse<Vec<AiSource>>;
pub type AiChatApiResponse = ApiResponse<AiChatResponse>;
