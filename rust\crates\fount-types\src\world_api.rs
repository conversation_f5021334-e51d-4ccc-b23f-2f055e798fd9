//! 世界API类型定义
//! 对应原文件: src/decl/WorldAPI.ts

use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use async_trait::async_trait;
use crate::{ApiResponse, Status, Info, Locale, Role, TimeStamp};

/// 世界初始化参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct WorldInit {
    pub username: String,
    pub worldname: String,
}

/// 频率数据
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct FrequencyData {
    pub charname: Option<String>,
    pub frequency: f64,
}

/// 世界信息接口 (对应 interfaces.info)
#[async_trait]
pub trait WorldInfoInterface {
    async fn update_info(&self, locales: Vec<Locale>) -> crate::Result<Info>;
}

/// 世界配置接口 (对应 interfaces.config)
#[async_trait]
pub trait WorldConfigInterface {
    async fn get_data(&self) -> crate::Result<serde_json::Value>;
    async fn set_data(&self, data: serde_json::Value) -> crate::Result<()>;
}

/// 世界聊天接口 (对应 interfaces.chat)
#[async_trait]
pub trait WorldChatInterface {
    async fn get_greeting(&self, arg: ChatReplyRequest, index: usize) -> crate::Result<Option<ChatReply>>;
    async fn get_group_greeting(&self, arg: ChatReplyRequest, index: usize) -> crate::Result<Option<ChatReply>>;
    async fn get_prompt(&self, arg: ChatReplyRequest, prompt_struct: PromptStruct, detail_level: u32) -> crate::Result<SinglePartPrompt>;
    async fn get_chat_log_for_charname(&self, arg: ChatReplyRequest, charname: String) -> crate::Result<Vec<ChatLogEntry>>;
    async fn add_chat_log_entry(&self, arg: ChatReplyRequest, entry: ChatLogEntry) -> crate::Result<()>;
    async fn after_add_chat_log_entry(&self, arg: ChatReplyRequest, freq_data: Vec<FrequencyData>) -> crate::Result<()>;
    async fn get_char_reply(&self, arg: ChatReplyRequest, charname: String) -> crate::Result<Option<ChatReply>>;
    async fn message_edit(&self, arg: MessageEditArgs) -> crate::Result<ChatReply>;
    async fn message_editing(&self, arg: MessageEditArgs) -> crate::Result<()>;
    async fn message_delete(&self, arg: MessageDeleteArgs) -> crate::Result<()>;
}

/// 世界API主接口 (对应 WorldAPI_t)
#[async_trait]
pub trait WorldApi {
    /// 获取世界信息
    fn get_info(&self) -> &Info;

    /// 世界安装时调用，失败时删除世界文件夹下所有文件
    async fn init(&mut self, stat: WorldInit) -> crate::Result<()>;

    /// 每次世界启动时调用，失败时弹出消息
    async fn load(&mut self, stat: WorldInit) -> crate::Result<()>;

    /// 每次世界卸载时调用
    async fn unload(&mut self, reason: String) -> crate::Result<()>;

    /// 世界卸载时调用
    async fn uninstall(&mut self, reason: String, from: String) -> crate::Result<()>;

    /// 获取信息接口
    fn info_interface(&self) -> Option<&dyn WorldInfoInterface>;

    /// 获取配置接口
    fn config_interface(&self) -> Option<&dyn WorldConfigInterface>;

    /// 获取聊天接口
    fn chat_interface(&self) -> Option<&dyn WorldChatInterface>;
}

/// 世界信息
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct World {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub creator_id: String,
    pub status: Status,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub metadata: HashMap<String, serde_json::Value>,
}

/// 世界创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateWorldRequest {
    pub name: String,
    pub description: String,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// 世界更新请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UpdateWorldRequest {
    pub name: Option<String>,
    pub description: Option<String>,
    pub status: Option<Status>,
    pub metadata: Option<HashMap<String, serde_json::Value>>,
}

/// 世界响应类型别名
pub type WorldResponse = ApiResponse<World>;
pub type WorldListResponse = ApiResponse<Vec<World>>;

/// 世界创建请求
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateWorldRequest {
    pub name: String,
    pub description: String,
}
