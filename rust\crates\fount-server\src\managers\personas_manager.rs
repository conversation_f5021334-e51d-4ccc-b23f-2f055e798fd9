//! 人格管理器
//! 对应原文件: src/server/managers/personas_manager.mjs

use std::collections::HashMap;
use uuid::Uuid;
use anyhow::Result;

/// 人格配置
#[derive(Debug, Clone)]
pub struct PersonaConfig {
    pub id: Uuid,
    pub name: String,
    pub description: String,
    pub traits: Vec<String>,
    pub enabled: bool,
}

/// 人格管理器
pub struct PersonasManager {
    personas: HashMap<Uuid, PersonaConfig>,
}

impl PersonasManager {
    pub fn new() -> Self {
        Self {
            personas: HashMap::new(),
        }
    }
    
    pub async fn create_persona(&mut self, name: String, description: String, traits: Vec<String>) -> Result<PersonaConfig> {
        let persona = PersonaConfig {
            id: Uuid::new_v4(),
            name,
            description,
            traits,
            enabled: true,
        };
        
        self.personas.insert(persona.id, persona.clone());
        Ok(persona)
    }
    
    pub fn get_persona(&self, id: &Uuid) -> Option<&PersonaConfig> {
        self.personas.get(id)
    }
    
    pub fn list_personas(&self) -> Vec<&PersonaConfig> {
        self.personas.values().collect()
    }
}

impl Default for PersonasManager {
    fn default() -> Self {
        Self::new()
    }
}
